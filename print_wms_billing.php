<?php
include('header.php');

$IsShowButton = true;
if(isset($_REQUEST['MyBizURLBillViewAPIKeyID']) && isset($_REQUEST['MyBizURLBillViewAPIKey'])) {
  
  if(!validateAPIAccess($_REQUEST['MyBizURLBillViewAPIKeyID'],$_REQUEST['MyBizURLBillViewAPIKey'])) {
    include('session.php');
  } else {
    $IsShowButton = false;
  }
} else {
  include('session.php');
}

$billmagic = $_REQUEST['billmagic'];
$qwmsbilling = "SELECT ba.*, 
                        CONCAT(eb.firstname, ' ',eb.lastname) as encodedbyName,
                        CONCAT(cb.firstname, ' ',cb.lastname) as checkedbyName,
                        CONCAT(ab.tfirstname, ' ',ab.lastname) as approvedbyName, billtoshipper
                FROM web_bill_accounts ba 
                LEFT JOIN web_webusers eb ON eb.user=ba.encodedby 
                LEFT JOIN web_webusers cb ON cb.user=ba.checkedby
                LEFT JOIN web_webusers ab ON ab.user=ba.approvedby
                WHERE ba.bill_magic='".$billmagic."' ";

$rwmsbilling = doQuery($qwmsbilling);
$rwmsbilling = mysql_fetch_array($rwmsbilling);

$account = $rwmsbilling['account'];

$bill_date = $rwmsbilling['bill_date']; 
$billvt_refnum = $rwmsbilling['billvt_refnum'];
$tin_num = $rwmsbilling['tin_num'];
$total_amount = $rwmsbilling['totalamount'];
$vat = $rwmsbilling['vat'];
$cutoff_period = $rwmsbilling['cutoff_period'];
$encodedby = $rwmsbilling['encodedby'];
$checkedby = $rwmsbilling['checkedby'];
$approvedby = $rwmsbilling['approvedby'];


$query=doQuery("SELECT * FROM mf_accounts mf 
                        LEFT JOIN  web_mf_accounts wmf ON mf.account=wmf.account AND mf.hubcode=wmf.hubcode 
                        WHERE mf.account='".$account."'");
		$clientInfo=mysql_fetch_array($query);
?>

<style>

body {
  margin: 0 auto;
  font-family: Arial, Verdana, sans-serif;
  font-size: 14px;
}
.style3{color: #FFFFFF}
<?php if(IsClient()) { ?>
#maincontainer{ text-align:center; vertical-align:top; height: 1056px; width:816px; margin: 0 auto;border:2px solid #ccc;}
<?php } else { ?>
#maincontainer{ text-align:center; vertical-align:top; height: 1056px; width:100%; margin: 0 auto;}
<?php } ?>
#maincontainer > table { margin-left: 30px;}
#printpagebutton{margin: 0 auto;text-align:center;}
.tabledetails { margin-top: 10px }
.tabledetails td{text-align:left}
@media print {
    body.modal-open .modal .modal-header,
    body.modal-open .modal .modal-body {
        visibility: hidden; /*hide modal body and header */
    }
	#myModal{display: none;}
}

</style>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        //Print the page content
		
		alert("Please use the billing invoice #<?php echo $lastVTRefNum ?>\n\nOS: Windows XP (Stand Alone/Virtual) (recommended)\nBrowser: Firefox v43 (recommeded)\n"+
			  "Note: It works also on different OS and Browser\n\nPaper Size: Letter\n\nMargins (Inches)\nLeft: 0.4\nRight: 0.3\nTop: 0.2\nBottom: 0.5\n");
		
        window.print()
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
    }
</script>
<body>

<?php if(IsClient()) { ?>
<br />
<br />
<?php } else {
if($IsShowButton) {
?>
<div id="printpagebutton">
<input type="button" value="Print Billing Invoice Number (<?php echo $billvt_refnum ?>)" onClick="printpage()"/>
<input type="button" data-toggle="modal" value="Add Note" data-target="#myModal"/>
</div>
<!-- Trigger the modal with a button -->
<?php } ?>
<!-- Modal -->
<div id="myModal" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
		<div class="modal-content">
		  <div class="modal-body">
			<form method="post" action="bill_print.php">
				<input type="hidden" name="billshipmentmagic" value="<?php echo $_REQUEST['billshipmentmagic'] ?>">
				<div class="form-group">
					<label for="comment">Note:</label>
					<textarea style="text-transform: uppercase;" class="form-control" name="note" value="<?php echo (isset($_POST['note'])) ? $_POST['note'] : $note ?>" rows="5" id="comment"></textarea>
				</div>
				<button type="submit" align="center" name="savenote" value="568gr5gh6790jk" class="btn btn-default">Save</button>
			</form>
		  </div>
		  <div class="modal-footer">
			<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		  </div>
		</div>

  </div>
</div>
<br/><br/>
<?php 
$browser = get_browser(null,true);
if(strtolower(trim($browser['platform'])) != 'winxp') {
?>
<br/>
<br/>
<br/>
<br/>
<?php }
}
?> 
<div id="maincontainer">
    <!--header-->
    <table border="0" cellpaddOing="0" cellspacing="0" width="95%" style="position:relative;">
        <tr>
            <td width="150" rowspan=4>&nbsp;</td>
            <td colspan="6" align="left"><?php echo $clientInfo['name'] ?></td>
        </tr>
        <tr>
            <td colspan="6">&nbsp;</td>
        </tr>
        <tr>
            <?php
            $billdate = str_replace(" ","&nbsp;",FormatDate($bill_date,3));
            ?>
            <td width="500" align="left" colspan="3"><?php echo $clientInfo['street'] ?>&nbsp;<?php echo $clientInfo['brgymun'] ?>&nbsp;<?php echo $clientInfo['cityprov'] ?></td>
            <td rowspan="2" width="460">&nbsp;</td>
            <td align="right" valign="bottom" width="200">&nbsp;&nbsp;<?php echo $billdate ?></td>
        </tr>
        <tr>
            <td width="180" align="left"><?php echo $clientInfo['tin_no'] ?></td>
            <td width="100"></td>
            <td width="220" style="position: relative;">&nbsp;<div style="width: 250px;position: absolute;top: 0;"align="left"><?php echo $clientInfo['business_style']<>'' ? $clientInfo['business_style'] :  $clientInfo['name'];  ?></div></td>
            <td align="right" >&nbsp;&nbsp;<?php echo $clientInfo['account'] ?></td>
        </tr>
    </table>
    <!--header end-->

    <br/>
    <br/>
    <br/>
    <br/>

    <!--Body-->
    <?php $cutoffperiod = str_replace(" ","&nbsp;",FormatDate($cutoff_period,3)); ?>
    <table cellpadding="0" class="tabledetails" cellspacing="0" border="0" width="85%" style="position:relative;min-height:300px;max-height: 300px;">  
    <tr><td colspan="2" height="10">TO BILL YOU FOR WAREHOUSING & DELIVERY CHARGES FOR THE CUTOFF PERIOD OF <?php echo $cutoffperiod ?></td></tr>
        <tr><td  colspan=2 height="10">(Please see attachment of shipment details & charges)</td></tr>
        <tr><td height="10" colspan=2><u>CHARGES SUMMARY</u></td></tr>    
            <?php 
            $qbillwarehouse = "SELECT * FROM web_bill_warehouse WHERE bill_magic='".$billmagic."' ";
            $rbillwarehouse = doQuery($qbillwarehouse);
            $number_of_records = 5;
            $total_charges_summary = 0;
            while($service = mysql_fetch_array($rbillwarehouse)){
                $total_charges_summary += $service['bill_amount'];
            ?>
            <tr height="10">  
                <td align="left"><?php echo $service['service_name'] ?></td>
                <td align="right" width="150"><?php echo number_format($service['bill_amount'],2) ?></td>
            </tr>

            <?php } ?>
      
            <?php 
            $gt_total_charges = round(($total_charges_summary * ((100+$clientInfo['vat']) / 100)),2);
            $vt_total_charges = $gt_total_charges - $total_charges_summary;
            $total_ewts = $clientInfo['Storage_Rentals_EWT'] + $clientInfo['Others_EWT'];
            $total_amount_due = $gt_total_charges - $total_ewts;

            $total_amount = $total_charges_summary;

            $db_added_vat = $clientInfo['vat'];
            $db_ewt = $clientInfo['Others_EWT'];

            $totalPayment=0;
                $VatSales=0;
                $Vat=0;
                $VatZeroRated=0;
                $NetVatSales = 0;
                $TotalGrossSales = 0;
                $TotalVatGrossSales = 0;
                $EWTAmount = 0;
                
            
            if($db_added_vat > 0) {
                    $NetVatSales = $total_amount;
                    $VatSales=($total_amount * ((100+$db_added_vat)/100));
                    $Vat=($VatSales-$total_amount);
                    $TotalGrossSales = ($NetVatSales + $Vat);
                    $TotalVatGrossSales = ($NetVatSales + $Vat);
                    $EWTAmount = ($NetVatSales * ($db_ewt/100));
                } else	{
                    $totalPayment=$total_amount;
                    $VatExempted=$total_amount;
                    $TotalGrossSales = $VatZeroRated = $total_amount;
                    $EWTAmount = ($VatExempted * ($db_ewt/100));
                }	
                
                $emptystring = "0.00";
            ?>
    </table>
    
    <table  cellpadding="1" border="0"  cellspacing="0" width="95%" style="margin-top:40px;" id="table_sales">
        <tr>
            <td align=right style="width:350px; line-height:1.8;">
                <?php //echo "Vatable Sales"; ?>
            </td>
            <td align=right style="width:250px; line-height:1.8;">
                <?php echo ($total_charges_summary > 0) ? number_format($total_charges_summary,2) : $emptystring ?>
            </td>
            <td align=right  style="width:350px; line-height:1.8;">
                <?php //echo "Total Sales" ?>
            </td>
            <td align=right style="line-height:1.8;">
            <?php
            if($gt_total_charges > 0 ) {
                echo number_format($gt_total_charges,2);
            } elseif($VatZeroRated > 0) {
                echo number_format($VatZeroRated,2);
            } else {
                echo $emptystring;
            } 
            ?>
            </td>
            <td align=right style="width:100px; line-height:1.8;" ></td>
        </tr>
            
        <tr>
            <td align=right>
            <?php //echo "VAT-Exempt Sales" ?>
            </td>
            <td align=right>
            <?php echo ($VatExempted > 0) ? number_format($VatExempted,2) : $emptystring ?>
            </td>
            <td align=right>
            <?php //echo "Less: VAT" ?>
            </td>
            <td align=right>
            <?php echo ($Vat > 0) ? number_format($Vat,2) : $emptystring ?>
            </td>
            <td align=right></td>
        </tr>
        
        <tr>
            <td align=right>
            <?php //echo "Zero-Rated Sales" ?>
            </td>
            <td align=right>
            <?php echo ($VatZeroRated > 0) ? number_format($VatZeroRated,2) : $emptystring  ?>
            </td>
            <td align=right>
            <?php //echo "Amount Net of VAT" ?>
            </td>
            <td align=right>
            <?php echo ($NetVatSales > 0) ? number_format($NetVatSales,2) :  $emptystring ?>
            </td>
            <td align=right></td>
        </tr>
            
        <tr>
            <td align=right>
            <?php //echo "VAT Amount"  ?>
            </td>
            <td align=right>
            <?php echo ($vt_total_charges > 0) ? number_format($vt_total_charges,2) : $emptystring ?> 
            </td>
            <td align=right>
            <?php //echo "Less: SC/PWD Discount" ?>
            </td>
            <td align=right>
            <?php echo $emptystring  ?>
            </td>
            <td align=right></td>
        </tr>
        
        <tr>
            <td align=right>
            <?php //echo "Total Sales" ?>
            </td>
            <td align=right>
            <?php
            echo ($TotalGrossSales > 0) ?  number_format($TotalGrossSales,2) : $emptystring ;
            ?>
            </td>
            <td align=right>
            <?php //echo "Amount Due" ?>
            </td>
            <td align=right>
            <?php if($NetVatSales > 0) {
                echo number_format($NetVatSales,2);
            } elseif($VatZeroRated > 0) {
                echo number_format($VatZeroRated,2);
            } else {
                echo $emptystring;
            } ?>
            </td>
            <td align=right></td>
        </tr>
        
        <tr>
            <td>
            </td>
            <td>
            </td>
            <td align=right>
            <?php //echo "Less: W/Tax" ?>
            </td>
            <td align=right>
            <?php echo ($EWTAmount > 0) ? number_format($EWTAmount,2) : $emptystring ?>
            </td>
            <td align=right></td>
        </tr>
        
        <tr>
            <td>
            </td>
            <td>
            </td>
            <td align=right>
            <?php //echo "Amount Net of W/Tax" //<?php echo number_format($VatZeroRated,2)  ?>
            </td>
            <td align=right>
            <?php echo (($NetVatSales - $EWTAmount) > 0) ? number_format($NetVatSales - $EWTAmount,2) : $emptystring ?>
            </td>
            <td align=right></td>
        </tr>

        
        <tr>
            <td>
            </td>
            <td>
            </td>
            <td align=right>
            <?php //echo "Add: VAT"  ?>
            </td>
            <td align=right>
            <?php echo ($Vat > 0) ? number_format($Vat,2) : $emptystring ?>
            </td>
            <td align=right></td>
        </tr>

        <tr>
            <td>
            </td>
            <td>
            </td>
            <td align=right>
            <?php //echo "Total Amount Due" ?>
            </td>
            <td align=right>
            <b>
            <?php echo "P";
            if($NetVatSales > 0) {
                echo number_format((($NetVatSales - $EWTAmount) + $Vat),2);
            } elseif($VatZeroRated > 0) {
                echo number_format(($VatZeroRated - $EWTAmount),2);
            } else {
                echo $emptystring;
            } ?>
            </b>	
            </td>
            <td></td>
        </tr>
	</table>
    <!--Body end-->
    <br/>
    <br/>
    <table>
        <tr>
            <td width="250"><?php echo (file_exists('img/usersignature/'.$encodedby.'.png')) ? '<img src="img/usersignature/'.$encodedby.'.png" style="max-height:40px;" border=0>' : '' ?></td>
            <td width="250"><?php echo (file_exists('img/usersignature/'.$checkedby.'.png')) ? '<img src="img/usersignature/'.$checkedby.'.png" style="max-height:40px;" border=0>' : '' ?></td>
            <td width="250"><?php echo (file_exists('img/usersignature/'.$approvedby.'.png')) ? '<img src="img/usersignature/'.$approvedby.'.png" style="max-height:40px;" border=0>' : '' ?></td>
        </tr>
        <tr>
            <td width="250"><?php echo (trim($rwmsbilling['encodedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$rwmsbilling['encodedbyName'].'</span>' : '' ?></td>
            <td width="250"><?php echo (trim($rwmsbilling['checkedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$rwmsbilling['checkedbyName'].'</span>' : '' ?></td>
            <td width="250"><?php echo (trim($rwmsbilling['approvedbyName'])!='') ? '<span style="padding: 0 20px 0 20px; border-top:1px solid #000">'.$rwmsbilling['approvedbyName'].'</span>' : '' ?></td>
        </tr>
    </table>
</div>
</body>
</html>